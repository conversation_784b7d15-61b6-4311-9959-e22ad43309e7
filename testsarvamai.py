from openai import OpenAI
from sarvamai import SarvamAI
import os
from dotenv import load_dotenv
from rich.console import Console
from rich.markdown import Markdown

Model_Ollama = "llama3.2:1b"
Model_Sarvam = "sarvam-m"

console = Console()

load_dotenv()

user_prumpt = "Desccribe how you will go about creating  and AI based class-notes generator for students in MCA, that is tailored to their semester exams. "



ollama_via_openai = OpenAI(
    api_key="ollama",
    base_url="http://localhost:11434/v1",
)

sarvamai = SarvamAI(
    api_subscription_key=os.getenv("SARVAM_API_KEY"),
)

messages = [
    {
        "role": "user", "content": 
    },
    {
        "role": "system", "content": "You are a helpful assistant. answer in markdown format."
    }
]

response = ollama_via_openai.chat.completions.create(
    model=Model_Sarvam,
    messages=messages
)